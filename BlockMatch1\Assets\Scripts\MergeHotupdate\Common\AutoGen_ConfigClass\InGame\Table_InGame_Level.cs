/************************************************
 * Config class : Table_InGame_Level
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_Level:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #ID-真实关卡顺序
        /// </summary>
        public int Sort { get; set; }
        
        /// <summary>
        /// #测试组
        /// </summary>
        public int AbTestGroup { get; set; }
        
        /// <summary>
        /// #JSON文件名
        /// </summary>
        public string JsonFileName { get; set; }
        
        /// <summary>
        /// 引用的块待选池组ID
        /// </summary>
        public int BlockGroupId { get; set; }
        
        /// <summary>
        /// #剧本组
        /// </summary>
        public int StoryGroup { get; set; }
        
        /// <summary>
        /// 收集宝石类型
        /// </summary>
        public List<int> GemstoneType { get; set; }
        
        /// <summary>
        /// 每类宝石数量
        /// </summary>
        public List<int> GemstoneCount { get; set; }
        
        /// <summary>
        /// 收集积分数量
        /// </summary>
        public int PointsCount { get; set; }
        
        /// <summary>
        /// 木箱数量
        /// </summary>
        public int WoodenCrateCount { get; set; }
        
        /// <summary>
        /// 鸟数量
        /// </summary>
        public int BirdCount { get; set; }
        
        /// <summary>
        /// 猫数量
        /// </summary>
        public int CatCount { get; set; }
        
        /// <summary>
        /// 首轮出块的ID，不配置则按照算法出块
        /// </summary>
        public List<int> FirstBlockId { get; set; }
        
        /// <summary>
        /// 块生成宝石的概率; （百分比）
        /// </summary>
        public int GemAppearWeight { get; set; }
        
        /// <summary>
        /// 生成宝石块的数量范围; (小于等于上限值)
        /// </summary>
        public List<int> GemNumberRange { get; set; }
        
        /// <summary>
        /// 宝石种类生成的权重（和GEMSTONETYPE字段对应）
        /// </summary>
        public List<int> GemstoneTypeWeight { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}